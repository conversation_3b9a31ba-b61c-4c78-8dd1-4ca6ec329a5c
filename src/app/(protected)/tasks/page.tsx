"use client";

import { useState, useEffect, useRef } from "react";
import { useUser } from "@stackframe/stack";
import { <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { SpotlightCard } from "@/components/ui/spotlight-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Plus, ArrowUpDown, MoreVertical, ChevronDown, ChevronRight, ChevronLeft, Trash, Edit, Trash2, Check, Circle, ArrowDownAZ, ArrowDownZA, CalendarArrowDown, CalendarArrowUp, Copy, FolderOpen, Tag as TagIcon, CheckCheck } from "lucide-react";
import { Task, TaskSortOption, List as ListType, Tag, Space } from "@/lib/db";
import { TaskList } from "./components/task-list";
import { AddTaskDialog } from "./components/add-task-dialog";
import { QuickAddTask } from "./components/quick-add-task";
import { SwipeContainer } from "./components/swipe-container";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Toggle } from "@/components/ui/toggle";
import { CleanupConfirmationDialog } from "./components/cleanup-confirmation-dialog";
// Dynamic imports for heavy modal components to reduce initial bundle size
import dynamic from "next/dynamic";
import { UnifiedHeader } from "./components/unified-header";

// Dynamically import modal components that aren't immediately needed
const EditListDialog = dynamic(() => import("./components/edit-list-dialog").then(mod => ({ default: mod.EditListDialog })), {
  ssr: false,
});
const DeleteListDialog = dynamic(() => import("./components/delete-list-dialog").then(mod => ({ default: mod.DeleteListDialog })), {
  ssr: false,
});
const AddListDialog = dynamic(() => import("./components/add-list-dialog").then(mod => ({ default: mod.AddListDialog })), {
  ssr: false,
});
const EditTagDialog = dynamic(() => import("./components/edit-tag-dialog").then(mod => ({ default: mod.EditTagDialog })), {
  ssr: false,
});
const DeleteTagDialog = dynamic(() => import("./components/delete-tag-dialog").then(mod => ({ default: mod.DeleteTagDialog })), {
  ssr: false,
});
const BulkDeleteConfirmationDialog = dynamic(() => import("./components/bulk-delete-confirmation-dialog").then(mod => ({ default: mod.BulkDeleteConfirmationDialog })), {
  ssr: false,
});
const BulkMoveToListDialog = dynamic(() => import("./components/bulk-move-to-list-dialog").then(mod => ({ default: mod.BulkMoveToListDialog })), {
  ssr: false,
});
const BulkTagApplicationDialog = dynamic(() => import("./components/bulk-tag-application-dialog").then(mod => ({ default: mod.BulkTagApplicationDialog })), {
  ssr: false,
});
const SpaceNavigationModal = dynamic(() => import("@/components/spaces/space-navigation-modal").then(mod => ({ default: mod.SpaceNavigationModal })), {
  ssr: false,
});
const CreateSpaceModal = dynamic(() => import("@/components/spaces/create-space-modal").then(mod => ({ default: mod.CreateSpaceModal })), {
  ssr: false,
});
import { getContrastTextColor } from "@/lib/list-colors";
import { useListColor } from "@/contexts/list-color-context";
import { useTagFilter } from "@/contexts/tag-filter-context";
import { QueryLoadingBoundary } from "@/components/ui/loading-boundary";
import { SkeletonTaskCard, SkeletonListTitle } from "@/components/ui/skeleton";
import {
  useListsBySpaceQuery,
  useDefaultListQuery,
  useTaskCountsQuery,
  useTasksQuery,

  useTasksByTagQuery,
  useTagsQuery,
  useEditListMutation,
  useReorderListsMutation,
  useDeleteCompletedTasksMutation,
  usePrefetchAdjacentLists,
  usePrefetchAllLists,
  useBulkRemoveTasksMutation,
  useBulkDuplicateTasksMutation,
  useBulkMoveTasksToListMutation,
  useBulkAddTagToTasksMutation,
  useBulkMarkTasksCompleteMutation,

  useSpacesQuery,
  useUserSettingsQuery,

} from "@/lib/queries";
import { searchTags, createNewTag } from "@/app/actions/tags";
import { TagFilter } from "@/lib/types";
import { UndoRedoProvider, useUndoRedoContext } from "@/contexts/undo-redo-context";
import { UndoRedoAction } from "@/lib/types";
import { v4 as uuidv4 } from "uuid";
import { useAppInitialization, useCacheWarming } from "@/hooks/use-app-initialization";
import { useSmartRefreshStrategy } from "@/hooks/use-smart-refresh";




function TasksPageContent() {
  const user = useUser({ or: "redirect" });
  const { setCurrentListColor } = useListColor();
  const { tagFilterState, setActiveTag, clearTagFilter } = useTagFilter();

  // Initialize app with critical data preloading
  useAppInitialization();
  const { warmCache } = useCacheWarming();

  // Smart refresh strategy for background data updates
  useSmartRefreshStrategy();

  // Undo/Redo functionality
  const { canUndo, canRedo, undo, redo, addAction, getLastAction, getLastRedoAction, clearHistory } = useUndoRedoContext();

  // TanStack Query hooks with optimized loading states
  const { data: spaces = [], isLoading: spacesLoading } = useSpacesQuery(user?.id || "");
  const { data: defaultList } = useDefaultListQuery(user?.id || "");
  const { data: taskCounts = {} } = useTaskCountsQuery(user?.id || "");
  const { data: availableTags = [] } = useTagsQuery(user?.id || "");
  const { data: userSettings, isLoading: userSettingsLoading } = useUserSettingsQuery(user?.id || "");

  // Local state
  const [currentList, setCurrentList] = useState<ListType | null>(null);
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false);
  const [sortType, setSortType] = useState<"position" | "title" | "due_date">("position");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [isCompletedSectionOpen, setIsCompletedSectionOpen] = useState(false);
  const [isCleanupDialogOpen, setIsCleanupDialogOpen] = useState(false);
  const [isEditListDialogOpen, setIsEditListDialogOpen] = useState(false);
  const [isDeleteListDialogOpen, setIsDeleteListDialogOpen] = useState(false);
  const [isAddListDialogOpen, setIsAddListDialogOpen] = useState(false);
  const [isEditTagDialogOpen, setIsEditTagDialogOpen] = useState(false);
  const [isDeleteTagDialogOpen, setIsDeleteTagDialogOpen] = useState(false);

  // Space-related state
  const [currentSpace, setCurrentSpace] = useState<{ id: string; name: string; icon?: string | null } | null>(null);
  const [isSpaceNavigationOpen, setIsSpaceNavigationOpen] = useState(false);
  const [isCreateSpaceOpen, setIsCreateSpaceOpen] = useState(false);

  // Load lists for current space only (optimized server-side filtering)
  const { data: lists = [], isLoading: listsLoading } = useListsBySpaceQuery(
    user?.id || "",
    currentSpace?.id || ""
  );

  // Arrow navigation logic
  const currentListIndex = lists.findIndex(list => list.id === currentList?.id);
  const canNavigateLeft = currentListIndex > 0;
  const canNavigateRight = currentListIndex < lists.length - 1;

  const navigateToNextList = () => {
    if (canNavigateRight) {
      const nextList = lists[currentListIndex + 1];
      handleListSelect(nextList);
    }
  };

  const navigateToPreviousList = () => {
    if (canNavigateLeft) {
      const previousList = lists[currentListIndex - 1];
      handleListSelect(previousList);
    }
  };

  // Bulk operation dialog states
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [isBulkMoveToListDialogOpen, setIsBulkMoveToListDialogOpen] = useState(false);
  const [isBulkTagApplicationDialogOpen, setIsBulkTagApplicationDialogOpen] = useState(false);

  // Task interaction mode state
  const [taskMode, setTaskMode] = useState<"completion" | "selection">("completion");
  const [selectedTaskIds, setSelectedTaskIds] = useState<Set<string>>(new Set());
  const [lastSelectedTaskId, setLastSelectedTaskId] = useState<string | null>(null);

  // Action icons visibility state for completion mode
  const [activeActionIconsTaskId, setActiveActionIconsTaskId] = useState<string | null>(null);

  // Task navigation handler for edit modal
  const handleNavigateToTask = (_task: Task) => {
    // This will be handled by the EditTaskDialog itself through its props
    // The navigation is managed by passing the allTasks array and currentTaskIndex
  };

  // Clear selected tasks and action icons when switching lists
  useEffect(() => {
    setSelectedTaskIds(new Set());
    setLastSelectedTaskId(null);
    setActiveActionIconsTaskId(null);
  }, [currentList?.id]);

  // Handler for action icons visibility changes
  const handleActionIconsChange = (taskId: string | null) => {
    setActiveActionIconsTaskId(taskId);
  };

  // Custom handler to track last selected task
  const handleTaskSelectionChange = (newSelectedIds: Set<string>) => {
    const previousIds = selectedTaskIds;

    // Find which task was just added (if any)
    const addedTasks = Array.from(newSelectedIds).filter(id => !previousIds.has(id));

    // Update the last selected task ID to the most recently added task
    if (addedTasks.length > 0) {
      setLastSelectedTaskId(addedTasks[addedTasks.length - 1]);
    } else if (newSelectedIds.size === 0) {
      // If no tasks are selected, clear the last selected
      setLastSelectedTaskId(null);
    } else if (!newSelectedIds.has(lastSelectedTaskId || '')) {
      // If the last selected task was deselected, pick another one
      const remainingIds = Array.from(newSelectedIds);
      setLastSelectedTaskId(remainingIds.length > 0 ? remainingIds[remainingIds.length - 1] : null);
    }

    setSelectedTaskIds(newSelectedIds);
  };

  // Inline list name editing states
  const [isEditingListName, setIsEditingListName] = useState(false);
  const [editedListName, setEditedListName] = useState("");
  const [isSavingListName, setIsSavingListName] = useState(false);
  const listNameInputRef = useRef<HTMLInputElement>(null);

  // Inline editing toggle state
  const [isInlineEditEnabled, setIsInlineEditEnabled] = useState(false);

  // Convert sortType and sortDirection to TaskSortOption
  const getSortOption = (): TaskSortOption => {
    if (sortType === "title") {
      return sortDirection === "asc" ? "title" : "title_desc";
    } else if (sortType === "due_date") {
      return sortDirection === "asc" ? "due_date" : "due_date_desc";
    }
    return "position";
  };

  // Tasks query - either by list or by tag
  const { data: allTasksByList = [], isLoading: tasksLoadingByList } = useTasksQuery(
    currentList?.id || "",
    getSortOption(),
    user?.id
  );

  const { data: tasksByTag = [], isLoading: tasksLoadingByTag } = useTasksByTagQuery(
    tagFilterState.activeTag?.id || "",
    user?.id || "",
    getSortOption()
  );

  // Filter out subtasks from the main task list - they'll be rendered by their parent TaskItem
  const tasksByList = allTasksByList.filter(task => !task.parent_task_id);

  // Use tag-filtered tasks if tag filter is active, otherwise use list tasks (parent tasks only)
  const tasks = tagFilterState.isActive ? tasksByTag : tasksByList;
  const tasksLoading = tagFilterState.isActive ? tasksLoadingByTag : tasksLoadingByList;





  // Mutations
  const editListMutation = useEditListMutation(user?.id || "");
  const reorderListsMutation = useReorderListsMutation(user?.id || "");
  const deleteCompletedTasksMutation = useDeleteCompletedTasksMutation(
    currentList?.id || "",
    getSortOption()
  );


  // Bulk operation mutations
  const bulkRemoveTasksMutation = useBulkRemoveTasksMutation(
    currentList?.id || "",
    getSortOption()
  );
  const bulkDuplicateTasksMutation = useBulkDuplicateTasksMutation(
    currentList?.id || "",
    getSortOption()
  );
  const bulkMoveTasksToListMutation = useBulkMoveTasksToListMutation(
    currentList?.id || "",
    getSortOption()
  );
  const bulkAddTagToTasksMutation = useBulkAddTagToTasksMutation(
    currentList?.id || "",
    getSortOption()
  );
  const bulkMarkTasksCompleteMutation = useBulkMarkTasksCompleteMutation(
    currentList?.id || "",
    getSortOption()
  );

  // Enhanced prefetching with tag data
  const { prefetchAdjacentLists } = usePrefetchAdjacentLists(
    lists,
    currentList?.id || null,
    getSortOption(),
    user?.id
  );

  // Prefetch all lists for swipe navigation with tag data
  const { prefetchAllLists } = usePrefetchAllLists(lists, getSortOption(), user?.id);



  // Set current space when spaces and user settings are loaded
  useEffect(() => {
    if (spaces.length > 0 && !currentSpace && userSettings) {
      let defaultSpace: Space | undefined;

      // First, try to find the user's configured default space
      if (userSettings.default_space_id) {
        defaultSpace = spaces.find(space => space.id === userSettings.default_space_id);
      }

      // If no default space is configured or found, fall back to the first space
      if (!defaultSpace) {
        defaultSpace = spaces[0];
      }

      setCurrentSpace(defaultSpace);
    }
  }, [spaces, currentSpace, userSettings]);

  // Calculate space loading state - show loading if:
  // 1. Spaces are still loading from server
  // 2. User settings are still loading (needed to determine default space)
  // 3. We have both spaces and settings but haven't determined current space yet
  const isSpaceLoading = Boolean(
    spacesLoading ||
    userSettingsLoading ||
    (spaces.length > 0 && userSettings && !currentSpace)
  );

  // Set current list when lists are loaded
  useEffect(() => {
    if (lists.length > 0 && !currentList) {
      setCurrentList(lists[0]);
    } else if (lists.length === 0 && defaultList && !currentList) {
      setCurrentList(defaultList);
    }
  }, [lists, defaultList, currentList]);

  // Update current list when lists cache changes (for optimistic updates)
  useEffect(() => {
    if (currentList && lists.length > 0) {
      const updatedCurrentList = lists.find(list => list.id === currentList.id);
      if (updatedCurrentList && (
        updatedCurrentList.name !== currentList.name ||
        updatedCurrentList.color !== currentList.color
      )) {
        setCurrentList(updatedCurrentList);
      }
    }
  }, [lists, currentList]);

  // Update list color context when current list or active tag changes
  useEffect(() => {
    if (tagFilterState.isActive && tagFilterState.activeTag) {
      // Use tag color when tag filter is active
      setCurrentListColor(tagFilterState.activeTag.color);
    } else {
      // Use list color when no tag filter is active
      setCurrentListColor(currentList?.color || null);
    }
  }, [currentList, tagFilterState.isActive, tagFilterState.activeTag, setCurrentListColor]);

  // Prefetch adjacent lists when current list changes
  useEffect(() => {
    if (currentList && lists.length > 0) {
      prefetchAdjacentLists();
    }
  }, [currentList, lists, prefetchAdjacentLists]);

  // Prefetch all lists and tags on app load for instant navigation
  useEffect(() => {
    if (lists.length > 0 && user?.id) {
      // Immediate prefetch for swipe navigation
      prefetchAllLists();

      // Delay cache warming to not interfere with initial load
      const timer = setTimeout(() => {
        warmCache();
      }, 3000); // Delay cache warming

      return () => clearTimeout(timer);
    }
  }, [lists, user?.id, prefetchAllLists, warmCache]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditingListName && listNameInputRef.current) {
      listNameInputRef.current.focus();
    }
  }, [isEditingListName]);

  // Keyboard shortcuts for undo/redo
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle shortcuts when not typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      if ((event.ctrlKey || event.metaKey) && event.key === 'z' && !event.shiftKey) {
        event.preventDefault();
        if (canUndo) {
          undo();
        }
      } else if (((event.ctrlKey || event.metaKey) && event.key === 'y') ||
                 ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'Z')) {
        event.preventDefault();
        if (canRedo) {
          redo();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [canUndo, canRedo, undo, redo]);

  const handleTaskAdded = (newTask?: Task) => {
    // TanStack Query handles optimistic updates automatically through mutations
    // Record action for undo/redo
    if (newTask && currentList) {
      const action: UndoRedoAction = {
        id: uuidv4(),
        type: 'task_create',
        timestamp: Date.now(),
        description: `Created task "${newTask.title}"`,
        task: newTask,
        listId: currentList.id,
      };
      addAction(action);
    }
  };

  const handleTaskUpdated = async (updatedTask?: Task, statusChanged?: boolean) => {
    // TanStack Query handles optimistic updates automatically through mutations
    // Record action for undo/redo if we have the updated task
    if (updatedTask && currentList) {
      // Find the original task to compare changes
      const originalTask = tasks.find(t => t.id === updatedTask.id);
      if (originalTask) {
        if (statusChanged) {
          // Record status change action
          const action: UndoRedoAction = {
            id: uuidv4(),
            type: 'task_status_change',
            timestamp: Date.now(),
            description: `${updatedTask.status === 'completed' ? 'Completed' : 'Uncompleted'} task "${updatedTask.title}"`,
            taskId: updatedTask.id,
            listId: currentList.id,
            previousStatus: originalTask.status,
            newStatus: updatedTask.status,
          };
          addAction(action);
        } else {
          // Record general edit action
          const action: UndoRedoAction = {
            id: uuidv4(),
            type: 'task_edit',
            timestamp: Date.now(),
            description: `Edited task "${updatedTask.title}"`,
            taskId: updatedTask.id,
            listId: currentList.id,
            previousData: {
              title: originalTask.title,
              description: originalTask.description,
              due_date: originalTask.due_date,
            },
            newData: {
              title: updatedTask.title,
              description: updatedTask.description,
              due_date: updatedTask.due_date,
            },
          };
          addAction(action);
        }
      }
    }
  };

  const handleTaskDeleted = async (deletedTaskId?: string) => {
    // TanStack Query handles optimistic updates automatically through mutations
    // Record action for undo/redo
    if (deletedTaskId && currentList) {
      const deletedTask = tasks.find(t => t.id === deletedTaskId);
      if (deletedTask) {
        const position = tasks.findIndex(t => t.id === deletedTaskId);
        const action: UndoRedoAction = {
          id: uuidv4(),
          type: 'task_delete',
          timestamp: Date.now(),
          description: `Deleted task "${deletedTask.title}"`,
          task: deletedTask,
          listId: currentList.id,
          position,
        };
        addAction(action);
      }
    }
  };

  const handleTasksReordered = (reorderedTasks: Task[]) => {
    // TanStack Query handles optimistic updates automatically through mutations
    // Record action for undo/redo
    if (currentList) {
      const previousOrder = tasks.map(task => task.id);
      const newOrder = reorderedTasks.map(task => task.id);

      const action: UndoRedoAction = {
        id: uuidv4(),
        type: 'task_reorder',
        timestamp: Date.now(),
        description: 'Reordered tasks',
        listId: currentList.id,
        previousOrder,
        newOrder,
      };
      addAction(action);
    }
  };

  const handleListRenamed = (updatedList?: ListType) => {
    // TanStack Query will automatically refetch and update the cache
    // Update the current list state immediately to reflect changes
    if (updatedList && currentList && updatedList.id === currentList.id) {
      setCurrentList(updatedList);
    }
  };

  // Inline list name editing handlers
  const handleListNameClick = () => {
    if (currentList && !isEditingListName && isInlineEditEnabled) {
      setEditedListName(currentList.name);
      setIsEditingListName(true);
    }
  };

  // Inline editing toggle handlers
  const handleInlineEditToggle = () => {
    setIsInlineEditEnabled(!isInlineEditEnabled);
    // If disabling inline edit while editing, cancel the edit
    if (isInlineEditEnabled && isEditingListName) {
      setEditedListName(currentList?.name || "");
      setIsEditingListName(false);
    }
  };



  const handleSaveListName = async () => {
    if (!currentList || !user || !editedListName.trim()) {
      // If name is empty, revert to original
      if (!editedListName.trim()) {
        setEditedListName(currentList?.name || "");
      }
      setIsEditingListName(false);
      return;
    }

    // Don't save if name hasn't changed
    if (editedListName.trim() === currentList.name) {
      setIsEditingListName(false);
      return;
    }

    setIsSavingListName(true);
    try {
      await editListMutation.mutateAsync({
        listId: currentList.id,
        data: { name: editedListName.trim() }
      });

      setIsEditingListName(false);
      // Update the current list in state immediately for better UX
      setCurrentList({ ...currentList, name: editedListName.trim() });
    } catch (error) {
      console.error("Error updating list name:", error);
      // Revert to original name on error
      setEditedListName(currentList.name);
      setIsEditingListName(false);
    } finally {
      setIsSavingListName(false);
    }
  };

  const handleListDeleted = () => {
    // TanStack Query will automatically refetch and update the cache
    // Set current list to first available list
    if (lists.length > 1) {
      const remainingLists = lists.filter(list => list.id !== currentList?.id);
      if (remainingLists.length > 0) {
        setCurrentList(remainingLists[0]);
      }
    } else {
      setCurrentList(null);
    }
  };

  const handleListSelect = (list: ListType) => {
    // Clear tag filter when selecting a list
    if (tagFilterState.isActive) {
      clearTagFilter();
    }
    // Clear undo/redo history when switching lists to avoid confusion
    if (currentList && list.id !== currentList.id) {
      clearHistory();
    }
    setCurrentList(list);
  };

  const handleListsReorder = async (reorderedLists: ListType[]) => {
    // Record action for undo/redo
    const previousOrder = lists.map(list => list.id);
    const newOrder = reorderedLists.map(list => list.id);

    const action: UndoRedoAction = {
      id: uuidv4(),
      type: 'list_reorder',
      timestamp: Date.now(),
      description: 'Reordered lists',
      previousOrder,
      newOrder,
    };
    addAction(action);

    const listIds = reorderedLists.map(list => list.id);
    try {
      await reorderListsMutation.mutateAsync(listIds);
    } catch (error) {
      console.error("Error reordering lists:", error);
    }
  };

  const handleAddListClick = () => {
    setIsAddListDialogOpen(true);
  };

  const handleListAdded = async (newList: ListType) => {
    // Switch to the new list - TanStack Query handles the cache updates
    setCurrentList(newList);
  };

  // Space handlers
  const handleSpaceClick = () => {
    setIsSpaceNavigationOpen(true);
  };

  const handleSpaceSelect = (space: { id: string; name: string; icon?: string | null }) => {
    setCurrentSpace(space);
    // Clear current list selection when switching spaces
    setCurrentList(null);
    // Clear tag filter when switching spaces
    if (tagFilterState.isActive) {
      clearTagFilter();
    }
  };

  const handleCreateSpaceClick = () => {
    setIsCreateSpaceOpen(true);
  };

  const handleSpaceCreated = (newSpace: { id: string; name: string; icon?: string | null }) => {
    setCurrentSpace(newSpace);
    // Clear current list selection when switching to new space
    setCurrentList(null);
  };

  const handleSpaceUpdated = (updatedSpace: Space) => {
    // Update current space if it's the one that was updated
    if (currentSpace && currentSpace.id === updatedSpace.id) {
      setCurrentSpace({
        id: updatedSpace.id,
        name: updatedSpace.name,
        icon: updatedSpace.icon
      });
    }
  };

  // Tag filter handlers
  const handleTagSelect = (tag: TagFilter) => {
    setActiveTag(tag);
    // Clear current list selection when filtering by tag
    setCurrentList(null);
  };

  const handleClearTagFilter = () => {
    clearTagFilter();
    // Only set default list if no current list is selected
    // This preserves the current list selection when clearing tag filter
    if (!currentList && defaultList) {
      setCurrentList(defaultList);
    }
  };

  const handleSearchTags = async (searchTerm: string) => {
    return await searchTags(user?.id || "", searchTerm);
  };

  const handleTagCreate = async (name: string, color: string) => {
    if (!user?.id) return null;
    return await createNewTag(user.id, name, color);
  };

  // Tag editing and deletion handlers
  const handleTagEdited = (updatedTag?: Tag) => {
    // TanStack Query will automatically refetch and update the cache
    // Update the active tag state if it was edited
    if (updatedTag && tagFilterState.activeTag && updatedTag.id === tagFilterState.activeTag.id) {
      setActiveTag({
        id: updatedTag.id,
        name: updatedTag.name,
        color: updatedTag.color,
      });
    }
  };

  const handleTagDeleted = () => {
    // Clear the tag filter and redirect to default list
    clearTagFilter();
    if (defaultList) {
      setCurrentList(defaultList);
    }
  };

  // Separate tasks into active and completed
  const activeTasks = tasks.filter(task => task.status !== "completed");
  const completedTasks = tasks.filter(task => task.status === "completed");



  // Get incomplete task count - use actual filtered tasks when in tag-filtered view
  const incompleteTaskCount = tagFilterState.isActive
    ? activeTasks.length  // Count actual filtered incomplete tasks
    : (currentList ? (taskCounts[currentList.id] || 0) : 0); // Use cached counts for list view

  const handleCleanupCompletedTasks = async () => {
    if (!user || !currentList) return;

    try {
      await deleteCompletedTasksMutation.mutateAsync(user.id);

    } catch (error) {
      console.error("Error deleting completed tasks:", error);
    }
  };

  // Bulk operation handlers
  const handleBulkDeleteTasks = async () => {
    if (!user || selectedTaskIds.size === 0) return;

    try {
      const taskIdsArray = Array.from(selectedTaskIds);
      await bulkRemoveTasksMutation.mutateAsync({
        taskIds: taskIdsArray,
        userId: user.id,
      });

      // Clear selection after successful deletion
      setSelectedTaskIds(new Set());
      setLastSelectedTaskId(null);

    } catch (error) {
      console.error("Error bulk deleting tasks:", error);
    }
  };

  const handleBulkDuplicateTasks = async () => {
    if (!user || selectedTaskIds.size === 0 || !currentList) return;

    try {
      const taskIdsArray = Array.from(selectedTaskIds);
      await bulkDuplicateTasksMutation.mutateAsync({
        taskIds: taskIdsArray,
        userId: user.id,
      });

      // Clear selection after successful duplication
      setSelectedTaskIds(new Set());
      setLastSelectedTaskId(null);

    } catch (error) {
      console.error("Error bulk duplicating tasks:", error);
    }
  };

  const handleBulkMoveToList = async (newListId: string) => {
    if (!user || selectedTaskIds.size === 0) return;

    try {
      const taskIdsArray = Array.from(selectedTaskIds);
      await bulkMoveTasksToListMutation.mutateAsync({
        taskIds: taskIdsArray,
        userId: user.id,
        newListId,
      });

      // Clear selection after successful move
      setSelectedTaskIds(new Set());
      setLastSelectedTaskId(null);

    } catch (error) {
      console.error("Error bulk moving tasks:", error);
    }
  };

  const handleBulkApplyTag = async (tagId: string) => {
    if (!user || selectedTaskIds.size === 0) return;

    try {
      const taskIdsArray = Array.from(selectedTaskIds);
      await bulkAddTagToTasksMutation.mutateAsync({
        taskIds: taskIdsArray,
        userId: user.id,
        tagId,
      });

      // Clear selection after successful tag application
      setSelectedTaskIds(new Set());
      setLastSelectedTaskId(null);

    } catch (error) {
      console.error("Error bulk applying tag:", error);
    }
  };

  const handleBulkMarkComplete = async () => {
    if (!user || selectedTaskIds.size === 0) return;

    try {
      const taskIdsArray = Array.from(selectedTaskIds);
      await bulkMarkTasksCompleteMutation.mutateAsync({
        taskIds: taskIdsArray,
        userId: user.id,
      });

      // Clear selection after successful completion
      setSelectedTaskIds(new Set());
      setLastSelectedTaskId(null);

    } catch (error) {
      console.error("Error bulk marking tasks complete:", error);
    }
  };

  const getSortIcon = () => {
    if (sortType === "position") {
      return <ArrowUpDown className="h-4 w-4" />;
    } else if (sortType === "title") {
      return sortDirection === "asc" ? <ArrowDownAZ className="h-4 w-4" /> : <ArrowDownZA className="h-4 w-4" />;
    } else if (sortType === "due_date") {
      return sortDirection === "asc" ? <CalendarArrowDown className="h-4 w-4" /> : <CalendarArrowUp className="h-4 w-4" />;
    }

    return <ArrowUpDown className="h-4 w-4" />;
  };

  return (
    <div className="space-y-0">
      {/* Unified Header */}
      <UnifiedHeader
        lists={lists}
        currentListId={currentList?.id || null}
        taskCounts={taskCounts}
        onListSelect={handleListSelect}
        onListsReorder={handleListsReorder}
        onAddListClick={handleAddListClick}
        currentSpace={currentSpace}
        onSpaceClick={handleSpaceClick}
        isSpaceLoading={isSpaceLoading}
        isInlineEditEnabled={isInlineEditEnabled}
        onInlineEditToggle={handleInlineEditToggle}
        canUndo={canUndo}
        canRedo={canRedo}
        undo={undo}
        redo={redo}
        getLastAction={getLastAction}
        getLastRedoAction={getLastRedoAction}
        isLoading={listsLoading}
        showSkeleton={listsLoading && lists.length === 0}
        availableTags={availableTags}
        onTagSelect={handleTagSelect}
        onTagCreate={handleTagCreate}
        onSearchTags={handleSearchTags}
        activeTag={tagFilterState.activeTag}
        onClearTagFilter={handleClearTagFilter}
      />

      <SwipeContainer
        lists={lists}
        currentListId={currentList?.id || null}
        onListChange={handleListSelect}
      >
        <div className="px-2 md:px-4 py-3 space-y-6">
          {/* Desktop Arrow Navigation - Fixed positioned at top of screen */}
          <div className="hidden md:block">
            {/* Left Arrow - Fixed position */}
            <Button
              variant="ghost"
              className={`fixed top-1/2 left-4 -translate-y-1/2 p-4 z-30 rounded-full border-0 ${
                canNavigateLeft
                  ? "text-muted-foreground hover:text-foreground hover:bg-muted/20 backdrop-blur-sm"
                  : "text-muted-foreground/30 cursor-not-allowed"
              }`}
              onClick={navigateToPreviousList}
              disabled={!canNavigateLeft}
              title="Previous list"
              style={{
                backgroundColor: 'transparent',
                borderColor: 'transparent'
              }}
            >
              <ChevronLeft style={{ width: '36px', height: '36px' }} />
              <span className="sr-only">Previous list</span>
            </Button>

            {/* Right Arrow - Fixed position */}
            <Button
              variant="ghost"
              className={`fixed top-1/2 right-4 -translate-y-1/2 p-4 z-30 rounded-full border-0 ${
                canNavigateRight
                  ? "text-muted-foreground hover:text-foreground hover:bg-muted/20 backdrop-blur-sm"
                  : "text-muted-foreground/30 cursor-not-allowed"
              }`}
              onClick={navigateToNextList}
              disabled={!canNavigateRight}
              title="Next list"
              style={{
                backgroundColor: 'transparent',
                borderColor: 'transparent'
              }}
            >
              <ChevronRight style={{ width: '36px', height: '36px' }} />
              <span className="sr-only">Next list</span>
            </Button>
          </div>

          {/* Main Content */}
          <div>
              <SpotlightCard
            className={(tagFilterState.isActive && tagFilterState.activeTag?.color) || currentList?.color ? "gradient-border-dynamic" : "gradient-border-muted"}
            listColor={(tagFilterState.isActive && tagFilterState.activeTag?.color) || currentList?.color || undefined}
            enableSpotlight={true}
            spotlightIntensity="subtle"
            style={(tagFilterState.isActive && tagFilterState.activeTag?.color) || currentList?.color ? {
              '--list-color': (tagFilterState.isActive && tagFilterState.activeTag?.color) || currentList?.color,
            } as React.CSSProperties : undefined}
          >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between gap-4">
            {/* Left side - List name and task count badge */}
            <div className="flex-1 min-w-0 mr-4 overflow-hidden">
              <div className="flex items-center gap-3">
                {isEditingListName ? (
                  <Input
                    ref={listNameInputRef}
                    value={editedListName}
                    onChange={(e) => setEditedListName(e.target.value)}
                    onBlur={handleSaveListName}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleSaveListName();
                      } else if (e.key === 'Escape') {
                        setEditedListName(currentList?.name || "");
                        setIsEditingListName(false);
                      }
                    }}
                    className="text-lg font-semibold h-8 py-0 border-none shadow-none focus-visible:ring-1 focus-visible:ring-ring bg-transparent pl-3 pr-3"
                    disabled={isSavingListName}
                  />
                ) : (
                  <>
                    {tagFilterState.isActive && tagFilterState.activeTag ? (
                      <h2 className="text-lg font-semibold truncate max-w-[200px] sm:max-w-[280px] md:max-w-[380px] lg:max-w-[480px] block">
                        {tagFilterState.activeTag.name}
                      </h2>
                    ) : currentList?.name ? (
                      <h2
                        className="text-lg font-semibold cursor-pointer hover:text-muted-foreground transition-colors truncate max-w-[200px] sm:max-w-[280px] md:max-w-[380px] lg:max-w-[480px] block"
                        onClick={handleListNameClick}
                      >
                        {currentList.name}
                      </h2>
                    ) : (
                      <SkeletonListTitle width="w-32" />
                    )}
                  </>
                )}

                {/* Task count badge - show for active list or tag filter with incomplete tasks */}
                {((currentList && !tagFilterState.isActive) || (tagFilterState.isActive && tagFilterState.activeTag)) && incompleteTaskCount > 0 && !isEditingListName && (
                  <Badge
                    className="text-xs border-transparent flex-shrink-0"
                    style={tagFilterState.isActive && tagFilterState.activeTag ? {
                      backgroundColor: tagFilterState.activeTag.color,
                      borderColor: tagFilterState.activeTag.color,
                      color: getContrastTextColor(tagFilterState.activeTag.color)
                    } : currentList?.color ? {
                      backgroundColor: currentList.color,
                      borderColor: currentList.color,
                      color: getContrastTextColor(currentList.color)
                    } : {
                      backgroundColor: "#f8f9fa",
                      borderColor: "#e9ecef",
                      color: "#4b5563"
                    }}
                  >
                    {incompleteTaskCount}
                  </Badge>
                )}
              </div>
            </div>

            {/* Right side - Sort controls and ellipsis menu */}
            <div className="flex items-center gap-2 flex-shrink-0">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 w-8 p-0" title="Sort options">
                  {getSortIcon()}
                  <span className="sr-only">Sort by</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setSortType("position")}>
                  <div className="flex items-center">
                    <ArrowUpDown className="mr-2 h-4 w-4" />
                    <span>Custom Order</span>
                  </div>
                </DropdownMenuItem>

                <DropdownMenuItem onClick={() => {
                  if (sortType === "title") {
                    // Toggle direction if already sorting by title
                    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
                  } else {
                    // Set to title sort with ascending as default
                    setSortType("title");
                    setSortDirection("asc");
                  }
                }}>
                  <div className="flex items-center">
                    {sortType === "title" ? (
                      sortDirection === "asc" ? <ArrowDownAZ className="mr-2 h-4 w-4" /> : <ArrowDownZA className="mr-2 h-4 w-4" />
                    ) : (
                      <ArrowDownAZ className="mr-2 h-4 w-4" />
                    )}
                    <span>Name</span>
                  </div>
                </DropdownMenuItem>

                <DropdownMenuItem onClick={() => {
                  if (sortType === "due_date") {
                    // Toggle direction if already sorting by due date
                    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
                  } else {
                    // Set to due date sort with ascending as default
                    setSortType("due_date");
                    setSortDirection("asc");
                  }
                }}>
                  <div className="flex items-center">
                    {sortType === "due_date" ? (
                      sortDirection === "asc" ? <CalendarArrowDown className="mr-2 h-4 w-4" /> : <CalendarArrowUp className="mr-2 h-4 w-4" />
                    ) : (
                      <CalendarArrowDown className="mr-2 h-4 w-4" />
                    )}
                    <span>Due Date</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>



            {/* Task Mode Toggle Button */}
            <Toggle
              pressed={taskMode === "selection"}
              onPressedChange={(pressed) => {
                const newMode = pressed ? "selection" : "completion";
                setTaskMode(newMode);
                // Clear selected tasks and action icons when switching modes
                if (newMode === "completion") {
                  setSelectedTaskIds(new Set());
                  setLastSelectedTaskId(null);
                } else {
                  setActiveActionIconsTaskId(null);
                }
              }}
              variant="outline"
              size="sm"
              className="h-8 w-8 p-0"
              title={taskMode === "completion" ? "Switch to Selection Mode" : "Switch to Completion Mode"}
            >
              {taskMode === "completion" ? (
                <Check className="h-4 w-4" />
              ) : (
                <Circle className="h-4 w-4" />
              )}
              <span className="sr-only">
                {taskMode === "completion" ? "Completion Mode" : "Selection Mode"}
              </span>
            </Toggle>

              {/* Ellipsis menu for list management and cleanup */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="h-4 w-4" />
                    <span className="sr-only">More options</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {taskMode === "selection" && selectedTaskIds.size > 0 ? (
                    <>
                      <DropdownMenuItem onClick={() => handleBulkMarkComplete()}>
                        <CheckCheck className="mr-2 h-4 w-4" />
                        Mark All Complete
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => setIsBulkMoveToListDialogOpen(true)}>
                        <FolderOpen className="mr-2 h-4 w-4" />
                        Move All to List
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleBulkDuplicateTasks()}>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate All
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setIsBulkTagApplicationDialogOpen(true)}>
                        <TagIcon className="mr-2 h-4 w-4" />
                        Apply Tag to All
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => setIsBulkDeleteDialogOpen(true)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete All
                      </DropdownMenuItem>
                    </>
                  ) : tagFilterState.isActive ? (
                    <>
                      <DropdownMenuItem onClick={() => setIsEditTagDialogOpen(true)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Tag
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setIsDeleteTagDialogOpen(true)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete Tag
                      </DropdownMenuItem>
                    </>
                  ) : (
                    <>
                      <DropdownMenuItem onClick={() => setIsEditListDialogOpen(true)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit List
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setIsDeleteListDialogOpen(true)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete List
                      </DropdownMenuItem>
                    </>
                  )}
                  {completedTasks.length > 0 && taskMode !== "selection" && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => setIsCleanupDialogOpen(true)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Completed Tasks
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-3 md:px-6">
          <QueryLoadingBoundary
            queries={[
              { isLoading: listsLoading && lists.length === 0 },
              { isLoading: tasksLoading && tasks.length === 0 }
            ]}
            fallback={
              <div className="space-y-4">
                <SkeletonTaskCard />
                <SkeletonTaskCard />
                <SkeletonTaskCard />
              </div>
            }
            loadingText="Loading tasks..."
          >
            {tasks.length === 0 ? (
              <div className="space-y-2">
                <div className="py-8 text-center text-muted-foreground">
                  {tagFilterState.isActive ? "No tasks found with this tag." : "No tasks yet. Add your first task below."}
                </div>
                {/* Quick Add Task Interface - only show when not filtering by tag */}
                {currentList && !tagFilterState.isActive && (
                  <QuickAddTask
                    onAddTaskClick={() => setIsAddTaskOpen(true)}
                    listId={currentList.id}
                    sortOption={getSortOption()}
                    onTaskAdded={handleTaskAdded}
                  />
                )}
              </div>
            ) : (
              <div className="space-y-6">
                {/* Active Tasks */}
                {activeTasks.length > 0 ? (
                  <TaskList
                    tasks={activeTasks}
                    allTasks={allTasksByList}
                    onTaskUpdated={handleTaskUpdated}
                    onTaskDeleted={handleTaskDeleted}
                    onTasksReordered={handleTasksReordered}
                    sortOption={getSortOption()}
                    listId={currentList?.id || ""}
                    listColor={tagFilterState.isActive && tagFilterState.activeTag ? tagFilterState.activeTag.color : currentList?.color}
                    taskCounts={taskCounts}
                    taskMode={taskMode}
                    selectedTaskIds={selectedTaskIds}
                    onTaskSelectionChange={handleTaskSelectionChange}
                    lastSelectedTaskId={lastSelectedTaskId}
                    showQuickAdd={!tagFilterState.isActive}
                    onAddTaskClick={() => setIsAddTaskOpen(true)}
                    isInlineEditEnabled={isInlineEditEnabled}
                    activeActionIconsTaskId={activeActionIconsTaskId}
                    onActionIconsChange={handleActionIconsChange}
                    onNavigateToTask={handleNavigateToTask}
                    isTagFiltered={tagFilterState.isActive}
                    onDragStart={() => setIsCompletedSectionOpen(false)}
                    currentSpaceId={currentSpace?.id}
                  />
                ) : (
                  <div className="space-y-2">
                    <div className="py-8 text-center text-muted-foreground">
                      {tagFilterState.isActive ? "No active tasks found with this tag." : "No active tasks. All tasks are completed!"}
                    </div>
                    {/* Quick Add Task Interface for empty state - only show when not filtering by tag */}
                    {currentList && !tagFilterState.isActive && (
                      <QuickAddTask
                        onAddTaskClick={() => setIsAddTaskOpen(true)}
                        listId={currentList.id}
                        sortOption={getSortOption()}
                        onTaskAdded={handleTaskAdded}
                      />
                    )}
                  </div>
                )}

                {/* Completed Tasks Section */}
                {completedTasks.length > 0 && (
                  <Collapsible
                    open={isCompletedSectionOpen}
                    onOpenChange={setIsCompletedSectionOpen}
                  >
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        className="flex items-center gap-2 w-full justify-start p-0 h-auto text-muted-foreground hover:text-foreground"
                      >
                        {isCompletedSectionOpen ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                        <span className="text-sm font-medium">
                          Completed Tasks ({completedTasks.length})
                        </span>
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-4">
                      <TaskList
                        tasks={completedTasks}
                        allTasks={allTasksByList}
                        onTaskUpdated={handleTaskUpdated}
                        onTaskDeleted={handleTaskDeleted}
                        onTasksReordered={handleTasksReordered}
                        sortOption={getSortOption()}
                        listId={currentList?.id || ""}
                        listColor={tagFilterState.isActive && tagFilterState.activeTag ? tagFilterState.activeTag.color : currentList?.color}
                        taskCounts={taskCounts}
                        taskMode={taskMode}
                        selectedTaskIds={selectedTaskIds}
                        onTaskSelectionChange={handleTaskSelectionChange}
                        lastSelectedTaskId={lastSelectedTaskId}
                        isInlineEditEnabled={isInlineEditEnabled}
                        activeActionIconsTaskId={activeActionIconsTaskId}
                        onActionIconsChange={handleActionIconsChange}
                        onNavigateToTask={handleNavigateToTask}
                        isTagFiltered={tagFilterState.isActive}
                        currentSpaceId={currentSpace?.id}
                      />
                    </CollapsibleContent>
                  </Collapsible>
                )}
              </div>
            )}
          </QueryLoadingBoundary>
        </CardContent>
          </SpotlightCard>
          </div>

          {/* Floating Add Task Button */}
          <div className="fixed bottom-20 z-40 transition-transform duration-200 hover:scale-110 md:bottom-4 left-4 md:left-[max(1rem,calc((100vw-896px)/2+1rem))]">
            <Button
              size="icon"
              className="h-14 w-14 rounded-full shadow-md"
              style={tagFilterState.isActive && tagFilterState.activeTag ? {
                backgroundColor: tagFilterState.activeTag.color,
                borderColor: tagFilterState.activeTag.color,
                color: getContrastTextColor(tagFilterState.activeTag.color)
              } : currentList?.color ? {
                backgroundColor: currentList.color,
                borderColor: currentList.color,
                color: getContrastTextColor(currentList.color)
              } : {
                backgroundColor: "#f8f9fa",
                borderColor: "#e9ecef",
                color: "#4b5563"
              }}
              onClick={() => setIsAddTaskOpen(true)}
            >
              <Plus className="h-6 w-6" />
              <span className="sr-only">Add new task</span>
            </Button>
          </div>

          <AddTaskDialog
            open={isAddTaskOpen}
            onOpenChange={setIsAddTaskOpen}
            onTaskAdded={handleTaskAdded}
            listId={tagFilterState.isActive ? (defaultList?.id || null) : (currentList?.id || null)}
            sortOption={getSortOption()}
            currentSpaceId={currentSpace?.id || ""}
          />

          <CleanupConfirmationDialog
            open={isCleanupDialogOpen}
            onOpenChange={setIsCleanupDialogOpen}
            onConfirm={handleCleanupCompletedTasks}
            completedTasksCount={completedTasks.length}
          />

          <EditListDialog
            open={isEditListDialogOpen}
            onOpenChange={setIsEditListDialogOpen}
            onListEdited={handleListRenamed}
            list={currentList}
          />

          <DeleteListDialog
            open={isDeleteListDialogOpen}
            onOpenChange={setIsDeleteListDialogOpen}
            onListDeleted={handleListDeleted}
            list={currentList}
            taskCount={tasks.length}
          />

          <AddListDialog
            open={isAddListDialogOpen}
            onOpenChange={setIsAddListDialogOpen}
            onListAdded={handleListAdded}
            spaceId={currentSpace?.id}
          />

          <EditTagDialog
            open={isEditTagDialogOpen}
            onOpenChange={setIsEditTagDialogOpen}
            onTagEdited={handleTagEdited}
            tag={tagFilterState.activeTag}
          />

          <DeleteTagDialog
            open={isDeleteTagDialogOpen}
            onOpenChange={setIsDeleteTagDialogOpen}
            onTagDeleted={handleTagDeleted}
            tag={tagFilterState.activeTag}
          />

          {/* Bulk Operation Dialogs */}
          <BulkDeleteConfirmationDialog
            open={isBulkDeleteDialogOpen}
            onOpenChange={setIsBulkDeleteDialogOpen}
            onConfirm={handleBulkDeleteTasks}
            selectedTasksCount={selectedTaskIds.size}
          />

          <BulkMoveToListDialog
            open={isBulkMoveToListDialogOpen}
            onOpenChange={setIsBulkMoveToListDialogOpen}
            onConfirm={handleBulkMoveToList}
            selectedTasksCount={selectedTaskIds.size}
            currentListId={currentList?.id || ""}
            currentSpaceId={currentSpace?.id || ""}
          />

          <BulkTagApplicationDialog
            open={isBulkTagApplicationDialogOpen}
            onOpenChange={setIsBulkTagApplicationDialogOpen}
            onConfirm={handleBulkApplyTag}
            selectedTasksCount={selectedTaskIds.size}
          />
        </div>
      </SwipeContainer>

      {/* Space Modals */}
      <SpaceNavigationModal
        open={isSpaceNavigationOpen}
        onOpenChange={setIsSpaceNavigationOpen}
        currentSpaceId={currentSpace?.id}
        onSpaceSelect={handleSpaceSelect}
        onCreateSpaceClick={handleCreateSpaceClick}
        onSpaceUpdated={handleSpaceUpdated}
      />

      <CreateSpaceModal
        open={isCreateSpaceOpen}
        onOpenChange={setIsCreateSpaceOpen}
        onSpaceCreated={handleSpaceCreated}
      />

    </div>
  );
}

export default function TasksPage() {
  return (
    <UndoRedoProvider>
      <TasksPageContent />
    </UndoRedoProvider>
  );
}
