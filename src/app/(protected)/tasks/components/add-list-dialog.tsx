"use client";

import { useState } from "react";
import { useUser } from "@stackframe/stack";
import { useAddListMutation } from "@/lib/queries";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ListColorPicker } from "@/components/ui/list-color-picker";
import { List } from "@/lib/db";
import { ChevronLeft } from "lucide-react";

interface AddListDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onListAdded: (newList: List) => void;
  spaceId?: string;
}

export function AddListDialog({
  open,
  onOpenChange,
  onListAdded,
  spaceId,
}: AddListDialogProps) {
  const user = useUser();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [color, setColor] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // TanStack Query mutation
  const addListMutation = useAddListMutation(user?.id || "", spaceId);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      setError("List name is required");
      return;
    }

    if (!user) {
      setError("You must be logged in to create a list");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const result = await addListMutation.mutateAsync({
        name: name.trim(),
        description: description.trim() || null,
        color: color,
        spaceId: spaceId,
      });

      if (result) {
        // Reset form and close dialog
        setName("");
        setDescription("");
        setColor(null);
        onOpenChange(false);
        onListAdded(result);
      } else {
        setError("Failed to create list. Please try again.");
      }
    } catch (err) {
      console.error("Error creating list:", err);
      setError("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form state when dialog is closed
    setName("");
    setDescription("");
    setColor(null);
    setError("");
    onOpenChange(false);
  };

  return (
    <MobileDialog open={open} onOpenChange={handleClose}>
      <MobileDialogContent className="sm:max-w-[425px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>New List</MobileDialogTitle>
        </VisuallyHidden>
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4 px-4 md:px-0">
            <div className="grid gap-2">
              <Input
                id="name"
                type="search"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="List name"
                autoComplete="off"
                spellCheck="false"
                disabled={isSubmitting}
                maxLength={120}
              />
            </div>

            <div className="grid gap-2">
              <RichTextEditor
                id="description"
                value={description}
                onChange={setDescription}
                placeholder="Add details about this list (optional)"
                maxLength={5000}
              />
            </div>

            <div className="grid gap-2">
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">Color:</span>
                <ListColorPicker
                  value={color}
                  onChange={setColor}
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {error && (
              <p className="text-sm font-medium text-destructive">{error}</p>
            )}
          </div>

          <div className="flex justify-end px-4 pb-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className="text-sm font-semibold text-muted-foreground hover:text-foreground disabled:text-muted-foreground/50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? "Creating..." : "Create"}
            </button>
          </div>
        </form>
      </MobileDialogContent>
    </MobileDialog>
  );
}
